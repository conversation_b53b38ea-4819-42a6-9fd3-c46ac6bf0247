{% extends 'admin/base.html.twig' %}

{% block title %}Video Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_video_index') }}">Videos</a></li>
<li class="breadcrumb-item active">{{ video.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-video mr-3" style="font-size: 2rem;"></i>
                        Video Details: {{ video.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href="{{ path('admin_video_edit', {'slug': video.slug}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Video">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Video Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href="{{ path('admin_video_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <!-- Video Title and Category (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-video text-primary mr-1"></i>
                                    Video Title
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 600; font-size: 1.1rem;">
                                    {{ video.title }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag text-primary mr-1"></i>
                                    Category
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-align-left text-primary mr-1"></i>
                            Description
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;">
                            {{ video.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Video Source Type and Access Level (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-source text-primary mr-1"></i>
                                    Video Source Type
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class="fab fa-youtube text-danger mr-2"></i>YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class="fas fa-shield-alt text-success mr-2"></i>VdoCipher
                                    {% else %}
                                        <i class="fas fa-upload text-info mr-2"></i>Direct Upload
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock text-primary mr-1"></i>
                                    Access Level
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {% if video.accessLevel == 'public_free' %}
                                        <i class="fas fa-globe text-success mr-2"></i>Public Free
                                    {% elseif video.accessLevel == 'login_required_free' %}
                                        <i class="fas fa-user text-warning mr-2"></i>Login Required Free
                                    {% else %}
                                        <i class="fas fa-crown text-danger mr-2"></i>Premium
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Pricing (if premium) -->
                    {% if video.accessLevel == 'premium' %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                    Price
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 600;">
                                    ${{ video.price ?? '0.00' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-clock text-primary mr-1"></i>
                                    Access Duration in Days
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.accessDuration ?? 'Unlimited' }} {% if video.accessDuration %}days{% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- YouTube URL (if YouTube) -->
                    {% if video.videoSourceType == 'youtube' and video.youtubeUrl %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fab fa-youtube text-danger mr-1"></i>
                            YouTube URL
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                            <a href="{{ video.youtubeUrl }}" target="_blank" class="text-decoration-none">{{ video.youtubeUrl }}</a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- VdoCipher ID (if VdoCipher) -->
                    {% if video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-shield-alt text-success mr-1"></i>
                            VdoCipher ID
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-family: monospace;">
                            {{ video.vdocipherVideoId }}
                        </div>
                    </div>
                    {% endif %}
                                            {% else %}
                                                <span class="badge bg-primary"><i class="fas fa-upload mr-1"></i>Direct Upload</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Access Level</label>
                                        <div class="enhanced-display-field">
                                            {% if video.accessLevel == 'public_free' %}
                                                <span class="badge bg-success"><i class="fas fa-globe mr-1"></i>Public Free</span>
                                            {% elseif video.accessLevel == 'login_required_free' %}
                                                <span class="badge bg-info"><i class="fas fa-user mr-1"></i>Login Required</span>
                                            {% else %}
                                                <span class="badge bg-warning"><i class="fas fa-crown mr-1"></i>Premium</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Price & Duration</label>
                                        <div class="enhanced-display-field">
                                            {{ video.formattedPrice }}
                                            {% if video.accessLevel == 'premium' and video.accessDuration %}
                                                <br><small class="text-muted">{{ video.formattedAccessDuration }} access</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Source Details -->
                        {% if video.youtubeUrl %}
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fab fa-youtube text-danger mr-1"></i>
                                YouTube URL
                            </label>
                            <div class="enhanced-display-field">
                                <a href="{{ video.youtubeUrl }}" target="_blank" class="text-primary">{{ video.youtubeUrl }}</a>
                            </div>
                        </div>
                        {% endif %}

                        {% if video.vdocipherVideoId %}
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-shield-alt text-success mr-1"></i>
                                VdoCipher Video ID
                            </label>
                            <div class="enhanced-display-field">
                                {{ video.vdocipherVideoId }}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Media Files -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-photo-video text-primary mr-1"></i>
                                Media Preview
                            </label>
                            <div class="row">
                                {% if video.thumbnail %}
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Thumbnail</label>
                                        <div class="image-container d-flex justify-content-center">
                                            <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                                 alt="Video Thumbnail"
                                                 class="enhanced-media-preview"
                                                 style="width: 450px; height: 300px; max-width: 100%; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Video Player</label>
                                        <div class="video-container d-flex justify-content-center">
                                            {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                                <iframe src="{{ video.youtubeEmbedUrl }}"
                                                        style="width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
                                                        frameborder="0"
                                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                        allowfullscreen>
                                                </iframe>
                                            {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                                <div class="vdocipher-placeholder d-flex align-items-center justify-content-center"
                                                     style="width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;">
                                                    <div class="text-center">
                                                        <i class="fas fa-shield-alt text-success" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                                        <h5>VdoCipher Protected Video</h5>
                                                        <p class="text-muted">ID: {{ video.vdocipherVideoId }}</p>
                                                    </div>
                                                </div>
                                            {% elseif video.videoFile %}
                                                <video controls
                                                       class="enhanced-media-preview"
                                                       style="width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                                    <source src="{{ asset('uploads/videos/files/' ~ video.videoFile) }}" type="video/mp4">
                                                    Your browser does not support the video tag.
                                                </video>
                                            {% else %}
                                                <div class="no-video-placeholder d-flex align-items-center justify-content-center"
                                                     style="width: 450px; height: 300px; max-width: 100%; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                                        <h5>No Video Source</h5>
                                                        <p>Please configure a video source</p>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Media Preview Styling */
.enhanced-media-preview {
    transition: all 0.3s ease;
}

.enhanced-media-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2) !important;
}

/* Responsive Media Preview */
@media (max-width: 768px) {
    .enhanced-media-preview {
        width: 100% !important;
        height: auto !important;
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .enhanced-media-preview {
        max-height: 200px;
    }
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
{% endblock %}

{% block print_body %}
    <!-- Video Information Section -->
    <div class="print-section">
        <div class="print-section-title">Video Information</div>
        <div class="print-info-grid">
            <div class="print-info-row">
                <div class="print-info-label">Title:</div>
                <div class="print-info-value">{{ video.title }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Category:</div>
                <div class="print-info-value">{{ video.category ?: 'Not specified' }}</div>
            </div>

            <div class="print-info-row">
                <div class="print-info-label">Status:</div>
                <div class="print-info-value">{{ video.isActive ? 'Active' : 'Inactive' }}</div>
            </div>

            <div class="print-info-row">
                <div class="print-info-label">Created Date:</div>
                <div class="print-info-value">{{ video.createdAt|date('F j, Y \\a\\t g:i A') }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Last Updated:</div>
                <div class="print-info-value">{{ video.updatedAt|date('F j, Y \\a\\t g:i A') }}</div>
            </div>
        </div>
    </div>

    {% if video.description %}
    <!-- Description Section -->
    <div class="print-section">
        <div class="print-section-title">Description</div>
        <div class="print-message-content" style="padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;">{{ video.description }}</div>
    </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
<script>
function printVideoDetails() {
    window.print();
}
</script>
{% endblock %}
