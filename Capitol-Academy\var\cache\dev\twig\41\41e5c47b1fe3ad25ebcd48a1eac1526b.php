<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/video/show.html.twig */
class __TwigTemplate_278483c31be13689339ad47e257f05e0 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'print_body' => [$this, 'block_print_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/show.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Video Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Video Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\">Videos</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Video Details: ";
        // line 24
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 24, $this->source); })()), "title", [], "any", false, false, false, 24), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href=\"";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 30, $this->source); })()), "id", [], "any", false, false, false, 30)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Video\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Video Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href=\"";
        // line 50
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Video Title and Slug Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Video Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-video text-primary mr-1\"></i>
                                        Video Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 78
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 78, $this->source); })()), "title", [], "any", false, false, false, 78), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Video Slug -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-link text-primary mr-1\"></i>
                                        URL Slug
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 91
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "slug", [], "any", true, true, false, 91) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 91, $this->source); })()), "slug", [], "any", false, false, false, 91)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 91, $this->source); })()), "slug", [], "any", false, false, false, 91), "html", null, true)) : ("Not set"));
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Details Row -->
                        <div class=\"row print-four-column clearfix\">
                            <!-- Video Category -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tags text-primary mr-1\"></i>
                                        Category
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 107
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "category", [], "any", true, true, false, 107) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 107, $this->source); })()), "category", [], "any", false, false, false, 107)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 107, $this->source); })()), "category", [], "any", false, false, false, 107), "html", null, true)) : ("Not specified"));
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Type -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Pricing
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 120
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 120, $this->source); })()), "isFree", [], "any", false, false, false, 120)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 121
            yield "                                            Free
                                        ";
        } else {
            // line 123
            yield "                                            \$";
            yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "price", [], "any", true, true, false, 123) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 123, $this->source); })()), "price", [], "any", false, false, false, 123)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 123, $this->source); })()), "price", [], "any", false, false, false, 123), "html", null, true)) : ("0.00"));
            yield "
                                        ";
        }
        // line 125
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 137
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 137, $this->source); })()), "isActive", [], "any", false, false, false, 137)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 138
            yield "                                            <span class=\"badge bg-success\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Active</span>
                                        ";
        } else {
            // line 140
            yield "                                            <span class=\"badge bg-secondary\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Inactive</span>
                                        ";
        }
        // line 142
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 154
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 154, $this->source); })()), "createdAt", [], "any", false, false, false, 154), "M d, Y h:i A"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                ";
        // line 167
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "description", [], "any", true, true, false, 167) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 167, $this->source); })()), "description", [], "any", false, false, false, 167)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 167, $this->source); })()), "description", [], "any", false, false, false, 167), "html", null, true)) : ("No description provided"));
        yield "
                            </div>
                        </div>

                        <!-- Video Source Information -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-source-branch text-primary mr-1\"></i>
                                Video Source & Access Information
                            </label>
                            <div class=\"row\">
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Source Type</label>
                                        <div class=\"enhanced-display-field\">
                                            ";
        // line 182
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 182, $this->source); })()), "videoSourceType", [], "any", false, false, false, 182) == "youtube")) {
            // line 183
            yield "                                                <span class=\"badge bg-danger\"><i class=\"fab fa-youtube mr-1\"></i>YouTube</span>
                                            ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 184
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 184, $this->source); })()), "videoSourceType", [], "any", false, false, false, 184) == "vdocipher")) {
            // line 185
            yield "                                                <span class=\"badge bg-success\"><i class=\"fas fa-shield-alt mr-1\"></i>VdoCipher</span>
                                            ";
        } else {
            // line 187
            yield "                                                <span class=\"badge bg-primary\"><i class=\"fas fa-upload mr-1\"></i>Direct Upload</span>
                                            ";
        }
        // line 189
        yield "                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Access Level</label>
                                        <div class=\"enhanced-display-field\">
                                            ";
        // line 196
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 196, $this->source); })()), "accessLevel", [], "any", false, false, false, 196) == "public_free")) {
            // line 197
            yield "                                                <span class=\"badge bg-success\"><i class=\"fas fa-globe mr-1\"></i>Public Free</span>
                                            ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 198
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 198, $this->source); })()), "accessLevel", [], "any", false, false, false, 198) == "login_required_free")) {
            // line 199
            yield "                                                <span class=\"badge bg-info\"><i class=\"fas fa-user mr-1\"></i>Login Required</span>
                                            ";
        } else {
            // line 201
            yield "                                                <span class=\"badge bg-warning\"><i class=\"fas fa-crown mr-1\"></i>Premium</span>
                                            ";
        }
        // line 203
        yield "                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Price & Duration</label>
                                        <div class=\"enhanced-display-field\">
                                            ";
        // line 210
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 210, $this->source); })()), "formattedPrice", [], "any", false, false, false, 210), "html", null, true);
        yield "
                                            ";
        // line 211
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 211, $this->source); })()), "accessLevel", [], "any", false, false, false, 211) == "premium") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 211, $this->source); })()), "accessDuration", [], "any", false, false, false, 211))) {
            // line 212
            yield "                                                <br><small class=\"text-muted\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 212, $this->source); })()), "formattedAccessDuration", [], "any", false, false, false, 212), "html", null, true);
            yield " access</small>
                                            ";
        }
        // line 214
        yield "                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Source Details -->
                        ";
        // line 221
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 221, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 221)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 222
            yield "                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fab fa-youtube text-danger mr-1\"></i>
                                YouTube URL
                            </label>
                            <div class=\"enhanced-display-field\">
                                <a href=\"";
            // line 228
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 228, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 228), "html", null, true);
            yield "\" target=\"_blank\" class=\"text-primary\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 228, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 228), "html", null, true);
            yield "</a>
                            </div>
                        </div>
                        ";
        }
        // line 232
        yield "
                        ";
        // line 233
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 233, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 233)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 234
            yield "                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-shield-alt text-success mr-1\"></i>
                                VdoCipher Video ID
                            </label>
                            <div class=\"enhanced-display-field\">
                                ";
            // line 240
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 240, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 240), "html", null, true);
            yield "
                            </div>
                        </div>
                        ";
        }
        // line 244
        yield "
                        <!-- Media Files -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-photo-video text-primary mr-1\"></i>
                                Media Preview
                            </label>
                            <div class=\"row\">
                                ";
        // line 252
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 252, $this->source); })()), "thumbnail", [], "any", false, false, false, 252)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 253
            yield "                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Thumbnail</label>
                                        <div class=\"image-container d-flex justify-content-center\">
                                            <img src=\"";
            // line 257
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 257, $this->source); })()), "thumbnail", [], "any", false, false, false, 257))), "html", null, true);
            yield "\"
                                                 alt=\"Video Thumbnail\"
                                                 class=\"enhanced-media-preview\"
                                                 style=\"width: 450px; height: 300px; max-width: 100%; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                        </div>
                                    </div>
                                </div>
                                ";
        }
        // line 265
        yield "
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Video Player</label>
                                        <div class=\"video-container d-flex justify-content-center\">
                                            ";
        // line 270
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 270, $this->source); })()), "videoSourceType", [], "any", false, false, false, 270) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 270, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 270))) {
            // line 271
            yield "                                                <iframe src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 271, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 271), "html", null, true);
            yield "\"
                                                        style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\"
                                                        frameborder=\"0\"
                                                        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                                        allowfullscreen>
                                                </iframe>
                                            ";
        } elseif (((CoreExtension::getAttribute($this->env, $this->source,         // line 277
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 277, $this->source); })()), "videoSourceType", [], "any", false, false, false, 277) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 277, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 277))) {
            // line 278
            yield "                                                <div class=\"vdocipher-placeholder d-flex align-items-center justify-content-center\"
                                                     style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;\">
                                                    <div class=\"text-center\">
                                                        <i class=\"fas fa-shield-alt text-success\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                        <h5>VdoCipher Protected Video</h5>
                                                        <p class=\"text-muted\">ID: ";
            // line 283
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 283, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 283), "html", null, true);
            yield "</p>
                                                    </div>
                                                </div>
                                            ";
        } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,         // line 286
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 286, $this->source); })()), "videoFile", [], "any", false, false, false, 286)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 287
            yield "                                                <video controls
                                                       class=\"enhanced-media-preview\"
                                                       style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                                    <source src=\"";
            // line 290
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 290, $this->source); })()), "videoFile", [], "any", false, false, false, 290))), "html", null, true);
            yield "\" type=\"video/mp4\">
                                                    Your browser does not support the video tag.
                                                </video>
                                            ";
        } else {
            // line 294
            yield "                                                <div class=\"no-video-placeholder d-flex align-items-center justify-content-center\"
                                                     style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                                    <div class=\"text-center text-muted\">
                                                        <i class=\"fas fa-video\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                        <h5>No Video Source</h5>
                                                        <p>Please configure a video source</p>
                                                    </div>
                                                </div>
                                            ";
        }
        // line 303
        yield "                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 316
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 317
        yield "<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Media Preview Styling */
.enhanced-media-preview {
    transition: all 0.3s ease;
}

.enhanced-media-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2) !important;
}

/* Responsive Media Preview */
@media (max-width: 768px) {
    .enhanced-media-preview {
        width: 100% !important;
        height: auto !important;
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .enhanced-media-preview {
        max-height: 200px;
    }
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 375
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_print_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        // line 376
        yield "    <!-- Video Information Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Video Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Title:</div>
                <div class=\"print-info-value\">";
        // line 382
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 382, $this->source); })()), "title", [], "any", false, false, false, 382), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Category:</div>
                <div class=\"print-info-value\">";
        // line 386
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 386, $this->source); })()), "category", [], "any", false, false, false, 386)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 386, $this->source); })()), "category", [], "any", false, false, false, 386), "html", null, true)) : ("Not specified"));
        yield "</div>
            </div>

            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Status:</div>
                <div class=\"print-info-value\">";
        // line 391
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 391, $this->source); })()), "isActive", [], "any", false, false, false, 391)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"));
        yield "</div>
            </div>

            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Created Date:</div>
                <div class=\"print-info-value\">";
        // line 396
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 396, $this->source); })()), "createdAt", [], "any", false, false, false, 396), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Last Updated:</div>
                <div class=\"print-info-value\">";
        // line 400
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 400, $this->source); })()), "updatedAt", [], "any", false, false, false, 400), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</div>
            </div>
        </div>
    </div>

    ";
        // line 405
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 405, $this->source); })()), "description", [], "any", false, false, false, 405)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 406
            yield "    <!-- Description Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Description</div>
        <div class=\"print-message-content\" style=\"padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;\">";
            // line 409
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 409, $this->source); })()), "description", [], "any", false, false, false, 409), "html", null, true);
            yield "</div>
    </div>
    ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 414
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 415
        yield "<script>
function printVideoDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/video/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  765 => 415,  752 => 414,  737 => 409,  732 => 406,  730 => 405,  722 => 400,  715 => 396,  707 => 391,  699 => 386,  692 => 382,  684 => 376,  671 => 375,  604 => 317,  591 => 316,  569 => 303,  558 => 294,  551 => 290,  546 => 287,  544 => 286,  538 => 283,  531 => 278,  529 => 277,  519 => 271,  517 => 270,  510 => 265,  499 => 257,  493 => 253,  491 => 252,  481 => 244,  474 => 240,  466 => 234,  464 => 233,  461 => 232,  452 => 228,  444 => 222,  442 => 221,  433 => 214,  427 => 212,  425 => 211,  421 => 210,  412 => 203,  408 => 201,  404 => 199,  402 => 198,  399 => 197,  397 => 196,  388 => 189,  384 => 187,  380 => 185,  378 => 184,  375 => 183,  373 => 182,  355 => 167,  339 => 154,  325 => 142,  321 => 140,  317 => 138,  315 => 137,  301 => 125,  295 => 123,  291 => 121,  289 => 120,  273 => 107,  254 => 91,  238 => 78,  207 => 50,  184 => 30,  175 => 24,  163 => 14,  150 => 13,  137 => 10,  133 => 9,  128 => 8,  115 => 7,  92 => 5,  69 => 3,  46 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Video Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_video_index') }}\">Videos</a></li>
<li class=\"breadcrumb-item active\">{{ video.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Video Details: {{ video.title }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href=\"{{ path('admin_video_edit', {'id': video.id}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Video\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Video Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href=\"{{ path('admin_video_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Video Title and Slug Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Video Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-video text-primary mr-1\"></i>
                                        Video Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ video.title }}
                                    </div>
                                </div>
                            </div>

                            <!-- Video Slug -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-link text-primary mr-1\"></i>
                                        URL Slug
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ video.slug ?? 'Not set' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Details Row -->
                        <div class=\"row print-four-column clearfix\">
                            <!-- Video Category -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tags text-primary mr-1\"></i>
                                        Category
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ video.category ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Type -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Pricing
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {% if video.isFree %}
                                            Free
                                        {% else %}
                                            \${{ video.price ?? '0.00' }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {% if video.isActive %}
                                            <span class=\"badge bg-success\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Active</span>
                                        {% else %}
                                            <span class=\"badge bg-secondary\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {{ video.createdAt|date('M d, Y h:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                {{ video.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Video Source Information -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-source-branch text-primary mr-1\"></i>
                                Video Source & Access Information
                            </label>
                            <div class=\"row\">
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Source Type</label>
                                        <div class=\"enhanced-display-field\">
                                            {% if video.videoSourceType == 'youtube' %}
                                                <span class=\"badge bg-danger\"><i class=\"fab fa-youtube mr-1\"></i>YouTube</span>
                                            {% elseif video.videoSourceType == 'vdocipher' %}
                                                <span class=\"badge bg-success\"><i class=\"fas fa-shield-alt mr-1\"></i>VdoCipher</span>
                                            {% else %}
                                                <span class=\"badge bg-primary\"><i class=\"fas fa-upload mr-1\"></i>Direct Upload</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Access Level</label>
                                        <div class=\"enhanced-display-field\">
                                            {% if video.accessLevel == 'public_free' %}
                                                <span class=\"badge bg-success\"><i class=\"fas fa-globe mr-1\"></i>Public Free</span>
                                            {% elseif video.accessLevel == 'login_required_free' %}
                                                <span class=\"badge bg-info\"><i class=\"fas fa-user mr-1\"></i>Login Required</span>
                                            {% else %}
                                                <span class=\"badge bg-warning\"><i class=\"fas fa-crown mr-1\"></i>Premium</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Price & Duration</label>
                                        <div class=\"enhanced-display-field\">
                                            {{ video.formattedPrice }}
                                            {% if video.accessLevel == 'premium' and video.accessDuration %}
                                                <br><small class=\"text-muted\">{{ video.formattedAccessDuration }} access</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Source Details -->
                        {% if video.youtubeUrl %}
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fab fa-youtube text-danger mr-1\"></i>
                                YouTube URL
                            </label>
                            <div class=\"enhanced-display-field\">
                                <a href=\"{{ video.youtubeUrl }}\" target=\"_blank\" class=\"text-primary\">{{ video.youtubeUrl }}</a>
                            </div>
                        </div>
                        {% endif %}

                        {% if video.vdocipherVideoId %}
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-shield-alt text-success mr-1\"></i>
                                VdoCipher Video ID
                            </label>
                            <div class=\"enhanced-display-field\">
                                {{ video.vdocipherVideoId }}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Media Files -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-photo-video text-primary mr-1\"></i>
                                Media Preview
                            </label>
                            <div class=\"row\">
                                {% if video.thumbnail %}
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Thumbnail</label>
                                        <div class=\"image-container d-flex justify-content-center\">
                                            <img src=\"{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}\"
                                                 alt=\"Video Thumbnail\"
                                                 class=\"enhanced-media-preview\"
                                                 style=\"width: 450px; height: 300px; max-width: 100%; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">Video Player</label>
                                        <div class=\"video-container d-flex justify-content-center\">
                                            {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                                <iframe src=\"{{ video.youtubeEmbedUrl }}\"
                                                        style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\"
                                                        frameborder=\"0\"
                                                        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                                        allowfullscreen>
                                                </iframe>
                                            {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                                <div class=\"vdocipher-placeholder d-flex align-items-center justify-content-center\"
                                                     style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;\">
                                                    <div class=\"text-center\">
                                                        <i class=\"fas fa-shield-alt text-success\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                        <h5>VdoCipher Protected Video</h5>
                                                        <p class=\"text-muted\">ID: {{ video.vdocipherVideoId }}</p>
                                                    </div>
                                                </div>
                                            {% elseif video.videoFile %}
                                                <video controls
                                                       class=\"enhanced-media-preview\"
                                                       style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                                    <source src=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\" type=\"video/mp4\">
                                                    Your browser does not support the video tag.
                                                </video>
                                            {% else %}
                                                <div class=\"no-video-placeholder d-flex align-items-center justify-content-center\"
                                                     style=\"width: 450px; height: 300px; max-width: 100%; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                                    <div class=\"text-center text-muted\">
                                                        <i class=\"fas fa-video\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                        <h5>No Video Source</h5>
                                                        <p>Please configure a video source</p>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Media Preview Styling */
.enhanced-media-preview {
    transition: all 0.3s ease;
}

.enhanced-media-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2) !important;
}

/* Responsive Media Preview */
@media (max-width: 768px) {
    .enhanced-media-preview {
        width: 100% !important;
        height: auto !important;
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .enhanced-media-preview {
        max-height: 200px;
    }
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
{% endblock %}

{% block print_body %}
    <!-- Video Information Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Video Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Title:</div>
                <div class=\"print-info-value\">{{ video.title }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Category:</div>
                <div class=\"print-info-value\">{{ video.category ?: 'Not specified' }}</div>
            </div>

            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Status:</div>
                <div class=\"print-info-value\">{{ video.isActive ? 'Active' : 'Inactive' }}</div>
            </div>

            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Created Date:</div>
                <div class=\"print-info-value\">{{ video.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Last Updated:</div>
                <div class=\"print-info-value\">{{ video.updatedAt|date('F j, Y \\\\a\\\\t g:i A') }}</div>
            </div>
        </div>
    </div>

    {% if video.description %}
    <!-- Description Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Description</div>
        <div class=\"print-message-content\" style=\"padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;\">{{ video.description }}</div>
    </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
<script>
function printVideoDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/video/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\video\\show.html.twig");
    }
}
